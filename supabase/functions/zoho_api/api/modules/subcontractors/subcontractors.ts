import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";
import { CreateSubcontractorDto, Subcontractor, SubcontractorInput, ZohoSubcontractorResponse } from "../../../types/subcontractor.ts";
import { HttpMethod } from "../../../types/common.ts";
import { SubcontractorsServiceUtils } from "./subcontractors.utils.ts";

export class SubcontractorsService {
  private zohoAPI: ZohoApi;
  private subcontractorsUtils: SubcontractorsServiceUtils;
  
  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
    this.subcontractorsUtils = new SubcontractorsServiceUtils();
  }

  /**
   * Create a new subcontractor in Zoho CRM
   */
  public async create(input: SubcontractorInput): Promise<ZohoSubcontractorResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Subcontractors`;
      const method: HttpMethod = "POST";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      // Transform input to Zoho format
      const dto = this.subcontractorsUtils.transformToZohoFormat(input);
      const body = { data: [dto] };

      console.log("Creating subcontractor in Zoho CRM:", dto);

      const response = await this.zohoAPI.call({
        endpoint,
        method,
        headers,
        body,
      });

      if (response && response.data && response.data.length > 0) {
        console.log(response)
        const createdRecord = response.data[0];
        
        return {
          success: true,
          recordId: createdRecord.details?.id || createdRecord.id,
          data: {
            message: "Subcontractor record created successfully in Zoho CRM",
            details: createdRecord.details || createdRecord,
            zohoData: dto
          }
        };
      } else {
        throw new Error("No data returned from Zoho API");
      }

    } catch (error) {
      console.error("Error creating subcontractor in Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create subcontractor in Zoho CRM"
      };
    }
  }

  /**
   * Search for existing subcontractor by email
   */
  public async searchByEmail(email: string): Promise<ZohoSubcontractorResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Leads/search?criteria=(Email:equals:${encodeURIComponent(email)})`;
      const method: HttpMethod = "GET";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      console.log(`Searching for subcontractor in Zoho CRM: ${email}`);

      const response = await this.zohoAPI.call({
        endpoint,
        method,
        headers,
      });

      if (response && response.data) {
        return {
          success: true,
          data: {
            message: response.data.length > 0 ? "Existing subcontractor found" : "No existing subcontractor found",
            records: response.data
          }
        };
      } else {
        return {
          success: true,
          data: {
            message: "No existing subcontractor found",
            records: []
          }
        };
      }

    } catch (error) {
      console.error("Error searching subcontractor in Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to search subcontractor in Zoho CRM"
      };
    }
  }

  /**
   * Update subcontractor status in Zoho CRM
   */
  public async updateStatus(recordId: string, status: string): Promise<ZohoSubcontractorResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Leads/${recordId}`;
      const method: HttpMethod = "PUT";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      const body = {
        data: [{
          Lead_Status: status,
          Modified_Time: new Date().toISOString()
        }]
      };

      console.log(`Updating subcontractor status in Zoho CRM: ${recordId} -> ${status}`);

      const response = await this.zohoAPI.call({
        endpoint,
        method,
        headers,
        body,
      });

      if (response && response.data && response.data.length > 0) {
        const updatedRecord = response.data[0];
        
        return {
          success: true,
          recordId: recordId,
          data: {
            message: "Subcontractor status updated successfully in Zoho CRM",
            details: updatedRecord.details || updatedRecord
          }
        };
      } else {
        throw new Error("No data returned from Zoho API");
      }

    } catch (error) {
      console.error("Error updating subcontractor status in Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to update subcontractor status in Zoho CRM"
      };
    }
  }

  /**
   * Get all subcontractors from Zoho CRM
   */
  public async findAll(): Promise<Subcontractor[]> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Leads?criteria=(Lead_Source:equals:Subcontractor Application)`;
      const method: HttpMethod = "GET";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      const response = await this.zohoAPI.call({ endpoint, method, headers });

      if (!response) {
        throw new Error("Failed to get response");
      }

      const data = response.data;

      if (!data || !Array.isArray(data)) {
        throw new Error("Failed to fetch subcontractors");
      }

      const subcontractors: Subcontractor[] = data.map((item) => {
        return {
          id: item.id ?? null,
          First_Name: item.First_Name ?? null,
          Last_Name: item.Last_Name ?? null,
          Email: item.Email ?? null,
          Mobile: item.Mobile ?? null,
          Address_Line1: item.Address_Line1 ?? null,
          Address_Line2: item.Address_Line2 ?? null,
          PostCode: item.PostCode ?? null,
          Date_of_birth: item.Date_of_birth ?? null,
          Hear_About_Us: item.Hear_About_Us ?? null,
          Gas_registered: item.Gas_registered ?? false,
          Years_Experience: item.Years_Experience ?? 0,
          Travel_Distance: item.Travel_Distance ?? 0,
          Has_Own_Van: item.Has_Own_Van ?? false,
          has_Own_Tools: item.has_Own_Tools ?? false,
          Work_Type: item.Work_Type ?? null,
          Central_London: item.Central_London ?? false,
          driving_License: item.driving_License ?? false,
          Public_Liability_Insurance: item.Public_Liability_Insurance ?? false,
          AvailableDays: item.AvailableDays ?? [],
          Accepted_Rates: item.Accepted_Rates ?? false,
          Out_Of_Hours_Work: item.Out_Of_Hours_Work ?? false,
          Emergency_Callouts: item.Emergency_Callouts ?? false,
          Preferred_WorkT_ype: item.Preferred_WorkT_ype ?? null,
          Additional_Qualifications: item.Additional_Qualifications ?? null,
          Subcontractors_Owner: item.Subcontractors_Owner ?? null,
          Lead_Source: item.Lead_Source ?? null,
          Lead_Status: item.Lead_Status ?? null,
          Description: item.Description ?? null,
          Created_Time: item.Created_Time ?? null,
          Modified_Time: item.Modified_Time ?? null,
        };
      });

      return subcontractors;

    } catch (error) {
      console.error("Error fetching subcontractors:", error);
      throw error;
    }
  }
}
