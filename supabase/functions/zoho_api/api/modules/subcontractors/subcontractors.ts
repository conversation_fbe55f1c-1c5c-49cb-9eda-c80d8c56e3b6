import { env } from "../../../../_shared/env.ts";
import AccessTokenStore from "../../../store.ts";
import { Zoho<PERSON><PERSON> } from "../../zoho.ts";
import { CreateSubcontractorDto, Subcontractor, SubcontractorInput, ZohoSubcontractorResponse } from "../../../types/subcontractor.ts";
import { HttpMethod } from "../../../types/common.ts";
import { SubcontractorsServiceUtils } from "./subcontractors.utils.ts";

export class SubcontractorsService {
  private zohoAPI: ZohoApi;
  private subcontractorsUtils: SubcontractorsServiceUtils;
  
  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
    this.subcontractorsUtils = new SubcontractorsServiceUtils();
  }

  /**
   * Create a new subcontractor in Zoho CRM
   */
  public async create(input: SubcontractorInput): Promise<ZohoSubcontractorResponse> {
    try {
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const endpoint = `${env.ZOHO_API_URL}/crm/v2/Leads`;
      const method: HttpMethod = "POST";
      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        "Content-Type": "application/json",
      };

      // Transform input to Zoho format
      const dto = this.subcontractorsUtils.transformToZohoFormat(input);
      const body = { data: [dto] };

      console.log("Creating subcontractor in Zoho CRM:", dto);

      const response = await this.zohoAPI.call({
        endpoint,
        method,
        headers,
        body,
      });

      if (response && response.data && response.data.length > 0) {
        console.log("response", response)
        const createdRecord = response.data[0];
        
        return {
          success: true,
          recordId: createdRecord.details?.id || createdRecord.id,
          data: {
            message: "Subcontractor record created successfully in Zoho CRM",
            details: createdRecord.details || createdRecord,
            zohoData: dto
          }
        };
      } else {
        console.log(" data returned from Zoho A")
        throw new Error("No data returned from Zoho API");
      }

    } catch (error) {
      console.error("Error creating subcontractor in Zoho:", error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create subcontractor in Zoho CRM"
      };
    }
  }
}
