import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Available days options
const AVAILABLE_DAYS = [
  "Monday",
  "Tuesday", 
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday"
] as const;

// Work type options
const WORK_TYPES = [
  "Boiler installations",
  "Boiler service and repair", 
  "Reactive maintenance",
  "General plumbing",
  "Commercial plumbing",
  "Bathroom installations",
  "Commercial heating",
  "Underfloor heating systems",
  "Oil Boiler systems",
  "LPG Systems",
  "Heating system controls and wiring"
] as const;

// Preferred work type options
const PREFERRED_WORK_TYPES = [
  "Residential",
  "Commercial", 
  "Both"
] as const;

// How they heard about us options
const HEAR_ABOUT_US_OPTIONS = [
  "Google",
  "Facebook",
  "Instagram", 
  "LinkedIn",
  "Word of mouth",
  "Referral",
  "Other"
] as const;

export const CreateSubcontractorDtoSchema = z.object({
  // Personal Information
  First_Name: z.string().min(1, "First name is required"),
  Last_Name: z.string().min(1, "Last name is required"),
  Email: z.string().email("Valid email is required"),
  Mobile: z.string().min(1, "Mobile number is required"),
  
  // Address Information
  Address_Line1: z.string().min(1, "Address line 1 is required"),
  Address_Line2: z.string().optional(),
  PostCode: z.string().min(1, "Post code is required"),
  
  // Personal Details
  Date_of_birth: z.string().min(1, "Date of birth is required"),
  Hear_About_Us: z.enum(HEAR_ABOUT_US_OPTIONS).optional(),
  
  // Professional Information
  Gas_registered: z.boolean().default(false),
  Years_Experience: z.number().min(0, "Years of experience must be 0 or greater"),
  Travel_Distance: z.number().min(0, "Travel distance must be 0 or greater"),
  Has_Own_Van: z.boolean().default(false),
  has_Own_Tools: z.boolean().default(false),
  Work_Type: z.enum(WORK_TYPES),
  Central_London: z.boolean().default(false),
  driving_License: z.boolean().default(false),
  Public_Liability_Insurance: z.boolean().default(false),
  
  // Availability and Preferences
  AvailableDays: z.array(z.enum(AVAILABLE_DAYS)).optional(),
  Accepted_Rates: z.boolean().default(false),
  Out_Of_Hours_Work: z.boolean().default(false),
  Emergency_Callouts: z.boolean().default(false),
  Preferred_WorkT_ype: z.enum(PREFERRED_WORK_TYPES),
  Additional_Qualifications: z.string().optional(),
  
  // System fields
  Subcontractors_Owner: z.string().optional(),
  Lead_Source: z.string().default("Subcontractor Application"),
  Lead_Status: z.string().default("New Application"),
  Description: z.string().optional(),
});

export type CreateSubcontractorDto = z.infer<typeof CreateSubcontractorDtoSchema>;

// Input schema for the API endpoint (matches the form data structure)
export const SubcontractorInputSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Valid email is required"),
  mobile: z.string().min(1, "Mobile number is required"),
  addressLine1: z.string().min(1, "Address line 1 is required"),
  addressLine2: z.string().optional(),
  postCode: z.string().min(1, "Post code is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  hearAboutUs: z.string().optional(),
  contactConsent: z.boolean().default(false),
  yearsExperience: z.number().min(0, "Years of experience must be 0 or greater"),
  travelDistance: z.number().min(0, "Travel distance must be 0 or greater"),
  additionalQualifications: z.string().optional(),
  referred: z.string().optional(),
  referrerName: z.string().optional(),
  
  // Boolean fields that come as strings from form data
  gasRegistered: z.boolean().default(false),
  hasOwnVan: z.boolean().default(false),
  hasOwnTools: z.boolean().default(false),
  centralLondon: z.boolean().default(false),
  drivingLicense: z.boolean().default(false),
  publicLiabilityInsurance: z.boolean().default(false),
  acceptedRates: z.boolean().default(false),
  outOfHoursWork: z.boolean().default(false),
  emergencyCallouts: z.boolean().default(false),
  
  // Work type and preferences
  workType: z.string().optional(),
  preferredWorkType: z.string().optional(),
  availableDays: z.array(z.string()).optional(),
});

export type SubcontractorInput = z.infer<typeof SubcontractorInputSchema>;

export const SubcontractorSchema = CreateSubcontractorDtoSchema.merge(
  z.object({ 
    id: z.string(),
    Created_Time: z.string().optional(),
    Modified_Time: z.string().optional(),
  })
);

export type Subcontractor = z.infer<typeof SubcontractorSchema>;

// Response type for Zoho API
export type ZohoSubcontractorResponse = {
  success: boolean;
  data?: any;
  error?: string;
  recordId?: string;
};
