import { Context } from "https://deno.land/x/oak@v14.0.0/mod.ts";
import { ReferralService } from "../services/referral.service.ts";

type ReferralInfoPackRequest = {
  email: string;
};

export async function handleSendReferralInfoPack(context: Context) {
  try {
    // Parse request body
    const body: ReferralInfoPackRequest = await context.request.body({ type: "json" }).value;

    console.log("Referral info pack request:", body);

    // Validate request
    if (!body.email) {
      context.response.status = 400;
      context.response.body = {
        error: {
          message: "Email is required",
        },
      };
      return;
    }

    // Create referral service instance
    const referralService = new ReferralService();

    // Validate email format
    const validation = referralService.validateReferralRequest(body.email);
    if (!validation.isValid) {
      context.response.status = 400;
      context.response.body = {
        error: {
          message: "Validation failed",
          details: validation.errors,
        },
      };
      return;
    }

    // Send referral info pack email
    const emailResult = await referralService.sendReferralInfoPack({
      to: body.email,
      subject: "Pleasant Plumbers - Referral Program Info Pack",
    });

    // Check if there was an error in the email service response
    if (emailResult && 'error' in emailResult) {
      console.error("Email sending failed:", emailResult.error);
      context.response.status = 500;
      context.response.body = {
        error: {
          message: "Failed to send referral info pack",
          details: emailResult.error,
        },
      };
      return;
    }

    console.log("Referral info pack sent successfully to:", body.email);

    // Success response
    context.response.status = 200;
    context.response.body = {
      data: {
        message: "Referral info pack sent successfully",
        email: body.email,
        result: emailResult,
      },
    };

  } catch (error) {
    console.error("Error in send-referral-info-pack function:", error);

    context.response.status = 500;
    context.response.body = {
      error: {
        message: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
}
