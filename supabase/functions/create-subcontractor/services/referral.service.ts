import { EmailService, EmailType } from "../../_shared/email.service.ts";
import { env } from "../../_shared/env.ts";

// Environment variables
const SENDGRID_API_KEY = env.SENDGRID_API_KEY;
const SENDGRID_SENDER_EMAIL = env.SENDGRID_SENDER_EMAIL;

type SendReferralInfoPackRequest = {
  to: string;
  from?: string;
  subject?: string;
  html?: string;
  emailType?: EmailType;
};

export class ReferralService {
  private emailService: EmailService;

  constructor() {
    if (!SENDGRID_API_KEY || !SENDGRID_SENDER_EMAIL) {
      throw new Error("Missing required environment variables: SENDGRID_API_KEY or SENDGRID_SENDER_EMAIL");
    }
    this.emailService = new EmailService(SENDGRID_API_KEY, SENDGRID_SENDER_EMAIL);
  }

  /**
   * Send referral info pack email
   */
  async sendReferralInfoPack(request: SendReferralInfoPackRequest) {
    try {
      // Generate custom HTML content for referral info pack
      const html = request.html || this.generateReferralInfoPackHtml(request.to);
      
      // Set default email type if not provided
      const emailType = request.emailType || EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED;

      // Send email
      const result = await this.emailService.sendEmail({
        to: request.to,
        from: request.from,
        subject: request.subject || "Pleasant Plumbers - Referral Info Pack",
        emailType: emailType,
        html: html,
      });

      return result;
    } catch (error) {
      console.error("Error sending referral info pack email:", error);
      return {
        error: {
          message: "Failed to send referral info pack email",
          details: error.message,
        },
      };
    }
  }

  /**
   * Generate referral info pack email HTML content
   */
  private generateReferralInfoPackHtml(email: string): string {
    return `
      <html>
        <head>
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              line-height: 1.6; 
              color: #333; 
              margin: 0; 
              padding: 0; 
              background-color: #f5f5f5;
            }
            .container { 
              max-width: 800px; 
              margin: 0 auto; 
              background-color: white;
              box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
            .header { 
              background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); 
              color: white; 
              padding: 40px 30px; 
              text-align: center; 
            }
            .header h1 { 
              margin: 0; 
              font-size: 2.5em; 
              font-weight: 300; 
            }
            .header p { 
              margin: 10px 0 0 0; 
              font-size: 1.2em; 
              opacity: 0.9; 
            }
            .content { 
              padding: 40px 30px; 
            }
            .section { 
              margin: 30px 0; 
              padding: 25px; 
              border-radius: 8px; 
              background-color: #f8f9fa; 
              border-left: 4px solid #007bff; 
            }
            .section h2 { 
              margin-top: 0; 
              color: #007bff; 
              font-size: 1.5em; 
            }
            .highlight-box { 
              background: linear-gradient(135deg, #28a745 0%, #20c997 100%); 
              color: white; 
              padding: 25px; 
              border-radius: 8px; 
              text-align: center; 
              margin: 25px 0; 
            }
            .highlight-box h3 { 
              margin: 0 0 10px 0; 
              font-size: 2em; 
            }
            .benefits-grid { 
              display: grid; 
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
              gap: 20px; 
              margin: 25px 0; 
            }
            .benefit-card { 
              background: white; 
              padding: 20px; 
              border-radius: 8px; 
              box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
              border-top: 3px solid #007bff; 
            }
            .benefit-card h4 { 
              margin: 0 0 10px 0; 
              color: #007bff; 
            }
            .cta-section { 
              background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); 
              color: white; 
              padding: 30px; 
              border-radius: 8px; 
              text-align: center; 
              margin: 30px 0; 
            }
            .cta-button { 
              display: inline-block; 
              background: white; 
              color: #007bff; 
              padding: 15px 30px; 
              text-decoration: none; 
              border-radius: 5px; 
              font-weight: bold; 
              margin: 15px 0; 
              transition: all 0.3s ease; 
            }
            .cta-button:hover { 
              background: #f8f9fa; 
              transform: translateY(-2px); 
            }
            .footer { 
              background: #343a40; 
              color: white; 
              padding: 30px; 
              text-align: center; 
            }
            .footer p { 
              margin: 5px 0; 
              opacity: 0.8; 
            }
            .steps { 
              counter-reset: step-counter; 
            }
            .step { 
              counter-increment: step-counter; 
              margin: 20px 0; 
              padding: 20px; 
              background: white; 
              border-radius: 8px; 
              box-shadow: 0 2px 5px rgba(0,0,0,0.1); 
              position: relative; 
              padding-left: 60px; 
            }
            .step::before { 
              content: counter(step-counter); 
              position: absolute; 
              left: 20px; 
              top: 20px; 
              background: #007bff; 
              color: white; 
              width: 30px; 
              height: 30px; 
              border-radius: 50%; 
              display: flex; 
              align-items: center; 
              justify-content: center; 
              font-weight: bold; 
            }
            .step h4 { 
              margin: 0 0 10px 0; 
              color: #007bff; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Pleasant Plumbers</h1>
              <p>Referral Program Information Pack</p>
            </div>
            
            <div class="content">
              <div class="highlight-box">
                <h3>Earn Up To £1,000</h3>
                <p>For every qualified engineer you refer to Pleasant Plumbers</p>
              </div>

              <div class="section">
                <h2>🎯 Why Refer Engineers to Pleasant Plumbers?</h2>
                <p>We're always looking for skilled, reliable engineers to join our growing team. When you refer someone who becomes a successful part of our network, we reward you generously for helping us find great talent.</p>
              </div>

              <div class="section">
                <h2>💰 How You Earn</h2>
                <div class="benefits-grid">
                  <div class="benefit-card">
                    <h4>£250 Bonus</h4>
                    <p>When your referral completes their first 10 hours of work</p>
                  </div>
                  <div class="benefit-card">
                    <h4>£250 Bonus</h4>
                    <p>When they reach 25 hours of completed work</p>
                  </div>
                  <div class="benefit-card">
                    <h4>£500 Final Bonus</h4>
                    <p>When they hit the 50-hour milestone</p>
                  </div>
                </div>
                <p><strong>Total potential earnings: £1,000 per successful referral</strong></p>
                <p>💡 <em>There's no limit to how many engineers you can refer!</em></p>
              </div>

              <div class="section">
                <h2>📋 How It Works</h2>
                <div class="steps">
                  <div class="step">
                    <h4>Share This Information</h4>
                    <p>Forward this email or share our application link with any qualified engineers you know.</p>
                  </div>
                  <div class="step">
                    <h4>They Apply</h4>
                    <p>When they fill out our application form, they'll need to enter your full name in the "Were you referred by someone?" field.</p>
                  </div>
                  <div class="step">
                    <h4>We Handle the Rest</h4>
                    <p>We'll process their application, conduct interviews, and get them started with work.</p>
                  </div>
                  <div class="step">
                    <h4>You Get Paid</h4>
                    <p>As they hit each milestone (10, 25, and 50 hours), we'll pay your bonuses directly to your bank account.</p>
                  </div>
                </div>
              </div>

              <div class="section">
                <h2>👷‍♂️ What We're Looking For</h2>
                <ul>
                  <li><strong>Gas Safe Registered Engineers</strong> (preferred but not essential)</li>
                  <li><strong>Experienced Plumbers</strong> with 2+ years experience</li>
                  <li><strong>Reliable professionals</strong> who take pride in their work</li>
                  <li><strong>Engineers with their own tools</strong> (van preferred but not required)</li>
                  <li><strong>Good communicators</strong> who work well with customers</li>
                </ul>
              </div>

              <div class="cta-section">
                <h3>Ready to Start Referring?</h3>
                <p>Share our application link with qualified engineers you know:</p>
                <a href="https://pleasantplumbers.com/subcontractors" class="cta-button">
                  Share Application Link
                </a>
                <p><small>Make sure they mention your name when applying!</small></p>
              </div>

              <div class="section">
                <h2>❓ Frequently Asked Questions</h2>
                <p><strong>Q: When do I get paid?</strong><br>
                A: Payments are made within 7 days of your referral reaching each milestone (10, 25, and 50 hours).</p>
                
                <p><strong>Q: Is there a limit to how many people I can refer?</strong><br>
                A: No! Refer as many qualified engineers as you like.</p>
                
                <p><strong>Q: What if my referral doesn't work out?</strong><br>
                A: You only get paid when they successfully complete work hours, so there's no risk to you.</p>
                
                <p><strong>Q: How do you track my referrals?</strong><br>
                A: When someone applies, they enter your full name in the referral field. We match this to your details.</p>
              </div>
            </div>

            <div class="footer">
              <p><strong>Pleasant Plumbers</strong></p>
              <p>London's Trusted Plumbing & Heating Specialists</p>
              <p>Email sent to: ${email}</p>
              <p>Questions? Reply to this email or call us at 020 XXXX XXXX</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate referral info pack request
   */
  validateReferralRequest(email: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Email validation
    if (!email) {
      errors.push("Email is required");
    } else if (!this.isValidEmail(email)) {
      errors.push("Invalid email format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
