// supabase/functions/create-subcontractor/services/subcontractor.service.ts

import { EmailService, EmailType } from "../../_shared/email.service.ts";
import { env } from "../../_shared/env.ts";

// Environment variables
// @ts-ignore: Deno is available in Supabase Edge Functions
const SENDGRID_API_KEY = env.SENDGRID_API_KEY;
// @ts-ignore: Deno is available in Supabase Edge Functions
const SENDGRID_SENDER_EMAIL = env.SENDGRID_SENDER_EMAIL;

type EmailAttachment = {
  content: string; // base64 encoded content
  filename: string;
  type?: string; // MIME type
  disposition?: string;
};

type SubcontractorData = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  gasRegistered: boolean;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  workType: string;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  availableDays?: string[];
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;
  preferredWorkType: string;
  additionalQualifications?: string;
};

type SendNotificationEmailRequest = {
  to: string;
  from?: string;
  subject?: string;
  html?: string;
  emailType?: EmailType;
  attachments?: EmailAttachment[];
  subcontractorData: SubcontractorData;
};

export class SubcontractorService {
  private emailService: EmailService;

  constructor() {
    if (!SENDGRID_API_KEY || !SENDGRID_SENDER_EMAIL) {
      throw new Error("Missing required environment variables: SENDGRID_API_KEY or SENDGRID_SENDER_EMAIL");
    }
    this.emailService = new EmailService(SENDGRID_API_KEY, SENDGRID_SENDER_EMAIL);
  }

  /**
   * Send notification email to subcontractor
   */
  async sendNotificationEmail(request: SendNotificationEmailRequest) {
    try {
      // Generate custom HTML content for subcontractor welcome email
      const html = request.html || this.generateWelcomeEmailHtml(request.subcontractorData);
      
      // Set default email type if not provided
      const emailType = request.emailType || EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED;

      // Send email with attachments and HTML content
      const result = await this.emailService.sendEmail({
        to: request.to,
        from: request.from,
        emailType: emailType,
        html: html,
        attachments: request.attachments,
      });

      return result;
    } catch (error) {
      console.error("Error sending notification email:", error);
      return {
        error: {
          message: "Failed to send notification email",
          details: error.message,
        },
      };
    }
  }

  /**
   * Generate welcome email HTML content
   */
  private generateWelcomeEmailHtml(data: SubcontractorData): string {
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .highlight { background-color: #e7f3ff; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Pleasant Plumbers!</h1>
            </div>
            <div class="content">
              <h2>New Subcontractor form submitted</h2>
              
              <div class="highlight">
                <h3>Application Summary:</h3>
                <ul>
                  <li><strong>Name:</strong> ${data.firstName} ${data.lastName}</li>
                  <li><strong>Email:</strong> ${data.email}</li>
                  <li><strong>Mobile:</strong> ${data.mobile}</li>
                  <li><strong>Years of Experience:</strong> ${data.yearsExperience}</li>
                  <li><strong>Gas Registered:</strong> ${data.gasRegistered ? 'Yes' : 'No'}</li>
                  <li><strong>Work Type:</strong> ${data.workType}</li>
                  <li><strong>Preferred Work Type:</strong> ${data.preferredWorkType}</li>
                </ul>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Validate subcontractor data
   */
  validateSubcontractorData(data: SubcontractorData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields validation
    const requiredFields = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!data[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Email validation
    if (data.email && !this.isValidEmail(data.email)) {
      errors.push("Invalid email format");
    }

    // Mobile validation
    if (data.mobile && !this.isValidMobile(data.mobile)) {
      errors.push("Invalid mobile number format");
    }

    // Years of experience validation
    if (data.yearsExperience !== undefined && (data.yearsExperience < 0 || data.yearsExperience > 50)) {
      errors.push("Years of experience must be between 0 and 50");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate mobile number format
   */
  private isValidMobile(mobile: string): boolean {
    // Basic UK mobile number validation
    const mobileRegex = /^(\+44|0)[0-9]{10}$/;
    return mobileRegex.test(mobile.replace(/\s/g, ''));
  }
}
